/**
 * 输出格式功能测试
 * 验证不同输出格式的正确性
 */

// 模拟格式化函数的核心逻辑
function formatResults(results, workflowIds, executionStats, outputFormat, customTemplate, groupByField) {
    switch (outputFormat) {
        case 'flat': {
            // 扁平化所有结果
            const allResults = [];
            for (const result of results) {
                if (result !== null) {
                    allResults.push(...result);
                }
            }
            return [allResults];
        }

        case 'grouped': {
            // 按指定字段分组结果
            const groups = {};
            
            for (let i = 0; i < results.length; i++) {
                const result = results[i];
                if (result !== null) {
                    for (const item of result) {
                        let groupKey;
                        if (groupByField === 'workflowId') {
                            groupKey = workflowIds[i] || 'unknown';
                        } else {
                            groupKey = String(item.json[groupByField] || 'unknown');
                        }
                        
                        if (!groups[groupKey]) {
                            groups[groupKey] = [];
                        }
                        groups[groupKey].push(item);
                    }
                }
            }

            // 转换为输出格式
            const groupedResults = [];
            for (const [groupKey, groupItems] of Object.entries(groups)) {
                groupedResults.push({
                    json: {
                        [groupByField]: groupKey,
                        items: groupItems.map(item => item.json),
                        count: groupItems.length,
                    },
                });
            }
            return [groupedResults];
        }

        case 'detailed': {
            // 详细结果格式
            const allResults = [];
            for (const result of results) {
                if (result !== null) {
                    allResults.push(...result);
                }
            }

            const detailedResult = {
                json: {
                    results: allResults.map(item => item.json),
                    summary: executionStats,
                    metadata: {
                        outputFormat: 'detailed',
                        workflowIds: [...new Set(workflowIds)],
                    },
                },
            };
            return [[detailedResult]];
        }

        case 'custom': {
            // 自定义模板格式
            try {
                const allResults = [];
                for (const result of results) {
                    if (result !== null) {
                        allResults.push(...result);
                    }
                }

                let template = customTemplate;
                
                // 替换模板变量
                template = template.replace(/\{\{results\}\}/g, JSON.stringify(allResults.map(item => item.json)));
                template = template.replace(/\{\{total\}\}/g, String(executionStats.total));
                template = template.replace(/\{\{successful\}\}/g, String(executionStats.successful));
                template = template.replace(/\{\{failed\}\}/g, String(executionStats.failed));
                template = template.replace(/\{\{executionTime\}\}/g, String(executionStats.executionTime));
                template = template.replace(/\{\{workflowId\}\}/g, JSON.stringify([...new Set(workflowIds)]));

                const customResult = JSON.parse(template);
                return [[{ json: customResult }]];
            } catch (error) {
                throw new Error(`自定义模板解析失败: ${error.message}`);
            }
        }

        default: {
            // 默认格式
            const finalResults = [];
            for (let i = 0; i < results.length; i++) {
                if (results[i] !== null) {
                    finalResults.push(results[i]);
                }
            }
            return finalResults;
        }
    }
}

// 测试数据
const testResults = [
    [{ json: { processed: "数据1", status: "success" } }],
    [{ json: { processed: "数据2", status: "success" } }],
    [{ json: { processed: "数据3", status: "success" } }],
    null, // 模拟失败的结果
];

const testWorkflowIds = ["123", "123", "456", "456"];
const testStats = {
    total: 4,
    successful: 3,
    failed: 1,
    executionTime: 2500,
};

console.log("=== 输出格式功能测试 ===\n");

// 测试1: 扁平化格式
console.log("测试1: 扁平化格式");
try {
    const result = formatResults(testResults, testWorkflowIds, testStats, 'flat');
    console.log("✅ 扁平化格式测试通过");
    console.log(`结果数量: ${result[0].length}`);
} catch (error) {
    console.log("❌ 扁平化格式测试失败:", error.message);
}

// 测试2: 分组格式
console.log("\n测试2: 分组格式");
try {
    const result = formatResults(testResults, testWorkflowIds, testStats, 'grouped', '', 'workflowId');
    console.log("✅ 分组格式测试通过");
    console.log(`分组数量: ${result[0].length}`);
} catch (error) {
    console.log("❌ 分组格式测试失败:", error.message);
}

// 测试3: 详细格式
console.log("\n测试3: 详细格式");
try {
    const result = formatResults(testResults, testWorkflowIds, testStats, 'detailed');
    console.log("✅ 详细格式测试通过");
    console.log(`包含统计信息: ${result[0][0].json.summary ? '是' : '否'}`);
} catch (error) {
    console.log("❌ 详细格式测试失败:", error.message);
}

// 测试4: 自定义格式
console.log("\n测试4: 自定义格式");
try {
    const customTemplate = '{"data": {{results}}, "total": {{total}}, "successful": {{successful}}}';
    const result = formatResults(testResults, testWorkflowIds, testStats, 'custom', customTemplate);
    console.log("✅ 自定义格式测试通过");
    console.log(`自定义字段存在: ${result[0][0].json.data ? '是' : '否'}`);
} catch (error) {
    console.log("❌ 自定义格式测试失败:", error.message);
}

// 测试5: 默认格式
console.log("\n测试5: 默认格式");
try {
    const result = formatResults(testResults, testWorkflowIds, testStats, 'default');
    console.log("✅ 默认格式测试通过");
    console.log(`结果数组数量: ${result.length}`);
} catch (error) {
    console.log("❌ 默认格式测试失败:", error.message);
}

console.log("\n=== 测试完成 ===");
