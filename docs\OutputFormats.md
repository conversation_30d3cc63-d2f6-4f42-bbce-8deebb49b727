# 输出格式使用指南

并行工作流执行器现在支持灵活的输出格式选项，让您可以根据具体需求自定义结果的组织方式。

## 快速开始

在节点配置中，找到"输出格式"参数，选择适合您需求的格式：

- **默认格式**: 保持与现有工作流的兼容性
- **扁平化结果**: 获得简单的结果列表
- **分组结果**: 按字段分组便于分析
- **详细结果**: 包含完整的执行统计
- **自定义结构**: 使用模板定制输出

## 格式详解

### 1. 默认格式 (default)
```json
// 配置
{
  "outputFormat": "default",
  "preserveOrder": true
}

// 输出（保持顺序时）
[
  [{"result": "data1"}],
  [{"result": "data2"}]
]

// 输出（不保持顺序时）
[
  {"result": "data1"},
  {"result": "data2"}
]
```

### 2. 扁平化结果 (flat)
```json
// 配置
{
  "outputFormat": "flat"
}

// 输出
[
  {"result": "data1"},
  {"result": "data2"},
  {"result": "data3"}
]
```

### 3. 分组结果 (grouped)
```json
// 配置
{
  "outputFormat": "grouped",
  "groupByField": "workflowId"
}

// 输出
[
  {
    "workflowId": "123",
    "items": [
      {"result": "data1"},
      {"result": "data2"}
    ],
    "count": 2
  }
]
```

### 4. 详细结果 (detailed)
```json
// 配置
{
  "outputFormat": "detailed"
}

// 输出
[
  {
    "results": [{"result": "data1"}],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1,
      "executionTime": 1500
    },
    "metadata": {
      "outputFormat": "detailed",
      "workflowIds": ["123", "456"]
    }
  }
]
```

### 5. 自定义结构 (custom)
```json
// 模板配置
{
  "outputFormat": "custom",
  "customTemplate": "{\"report\": {\"data\": {{results}}, \"stats\": {\"total\": {{total}}, \"time\": {{executionTime}}}}}"
}

// 输出
[
  {
    "report": {
      "data": [{"result": "data1"}],
      "stats": {
        "total": 2,
        "time": 1200
      }
    }
  }
]
```

## 自定义模板变量

在自定义模板中，您可以使用以下变量：

| 变量 | 描述 | 示例值 |
|------|------|--------|
| `{{results}}` | 所有结果数据 | `[{"result": "data1"}]` |
| `{{total}}` | 总执行数量 | `3` |
| `{{successful}}` | 成功执行数量 | `2` |
| `{{failed}}` | 失败执行数量 | `1` |
| `{{executionTime}}` | 总执行时间（毫秒） | `1500` |
| `{{workflowId}}` | 工作流ID列表 | `["123", "456"]` |

## 使用场景建议

### 数据分析场景
使用**分组格式**按类别分析结果：
```json
{
  "outputFormat": "grouped",
  "groupByField": "category"
}
```

### 监控和调试
使用**详细格式**获取完整的执行信息：
```json
{
  "outputFormat": "detailed",
  "includeMetadata": true
}
```

### 报告生成
使用**自定义格式**生成特定的报告结构：
```json
{
  "outputFormat": "custom",
  "customTemplate": "{\"report_date\": \"2024-12-29\", \"data\": {{results}}, \"summary\": {\"processed\": {{total}}, \"success_rate\": \"{{successful}}/{{total}}\"}}"
}
```

### 简单处理
使用**扁平化格式**获得简单的结果列表：
```json
{
  "outputFormat": "flat"
}
```

## 注意事项

1. **模板语法**: 自定义模板必须是有效的JSON格式
2. **变量替换**: 确保变量替换后的结果是有效的JSON值
3. **性能影响**: 复杂格式可能会增加少量处理时间
4. **兼容性**: 默认格式保持与现有工作流的完全兼容

## 错误处理

如果自定义模板格式错误，节点会抛出详细的错误信息：
```
自定义模板解析失败: Unexpected token } in JSON at position 15
```

确保您的模板语法正确，特别注意：
- 使用双引号包围字符串
- 正确的花括号匹配
- 有效的JSON结构

## 示例代码

查看 `examples/output-formats-demo.ts` 文件获取完整的使用示例和演示代码。
