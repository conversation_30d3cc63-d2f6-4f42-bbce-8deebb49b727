# 工作流定义使用指南

## 概述

并行工作流执行器现在支持通过连线传递工作流定义，实现真正的动态工作流执行。这个功能允许您：

- 动态创建和执行工作流
- 根据输入数据构建不同的工作流结构
- 实现工作流的程序化生成和执行

## 快速开始

### 1. 节点配置

1. 将并行工作流执行器节点添加到您的工作流中
2. 在"工作流来源"中选择"从连线获取工作流定义"
3. 配置"工作流定义字段"（默认：`workflow`）
4. 选择"工作流定义格式"（推荐：`n8n`）

### 2. 连接设置

并行工作流执行器现在有两个输入端口：

- **数据输入**（第一个端口）：接收要处理的数据
- **工作流定义**（第二个端口）：接收工作流定义

### 3. 基本示例

```json
// 数据输入
[
  {"userId": "user1", "action": "process"},
  {"userId": "user2", "action": "validate"}
]

// 工作流定义输入
[
  {
    "workflow": {
      "name": "动态处理工作流",
      "nodes": [
        {
          "parameters": {},
          "name": "Start",
          "type": "n8n-nodes-base.start",
          "position": [240, 300]
        },
        {
          "parameters": {
            "values": {
              "string": [{"name": "result", "value": "处理完成"}]
            }
          },
          "name": "Set",
          "type": "n8n-nodes-base.set",
          "position": [460, 300]
        }
      ],
      "connections": {
        "Start": {"main": [["Set"]]}
      }
    }
  }
]
```

## 工作流定义格式

### 1. n8n工作流JSON格式（推荐）

这是标准的n8n工作流导出格式：

```json
{
  "workflow": {
    "name": "我的动态工作流",
    "nodes": [...],
    "connections": {...},
    "active": false,
    "settings": {},
    "staticData": {}
  }
}
```

**优点**：
- 完整的工作流定义
- 支持所有n8n功能
- 可以直接从n8n导出获得

### 2. 节点数组格式

仅包含节点定义的简化格式：

```json
{
  "workflow": [
    {
      "parameters": {},
      "name": "Start",
      "type": "n8n-nodes-base.start"
    },
    {
      "parameters": {...},
      "name": "Process",
      "type": "n8n-nodes-base.set"
    }
  ]
}
```

**优点**：
- 简化的定义
- 适合程序化生成
- 自动添加基本连接

### 3. 自定义格式

您可以定义自己的格式，节点会按原样处理：

```json
{
  "workflow": {
    "customField": "自定义值",
    "nodes": [...],
    "myConnections": {...}
  }
}
```

## 执行模式

### Each模式（推荐）

每个数据项目使用对应的工作流定义：

- 数据项目1 → 工作流定义1
- 数据项目2 → 工作流定义2
- 如果工作流定义数量少于数据项目，会重复使用最后一个定义

### Once模式

所有数据项目使用第一个工作流定义一次性处理。

## 高级用法

### 1. 动态工作流生成

使用Function节点或Code节点动态生成工作流定义：

```javascript
// 在Function节点中
const workflowDef = {
  name: `动态工作流_${$json.userId}`,
  nodes: [
    {
      parameters: {},
      name: "Start",
      type: "n8n-nodes-base.start",
      position: [240, 300]
    },
    {
      parameters: {
        values: {
          string: [
            {
              name: "result",
              value: `为用户 ${$json.userId} 处理的结果`
            }
          ]
        }
      },
      name: "CustomProcess",
      type: "n8n-nodes-base.set",
      position: [460, 300]
    }
  ],
  connections: {
    "Start": {"main": [["CustomProcess"]]}
  }
};

return {
  json: {
    workflow: workflowDef
  }
};
```

### 2. 条件工作流选择

根据输入数据选择不同的工作流结构：

```javascript
// 根据action类型选择不同的工作流
const action = $json.action;
let workflowDef;

if (action === 'process') {
  workflowDef = {
    name: "数据处理工作流",
    nodes: [/* 处理节点 */]
  };
} else if (action === 'validate') {
  workflowDef = {
    name: "数据验证工作流", 
    nodes: [/* 验证节点 */]
  };
}

return { json: { workflow: workflowDef } };
```

### 3. 模板化工作流

使用模板系统生成工作流：

```javascript
// 工作流模板
const template = {
  name: "模板工作流",
  nodes: [
    {
      parameters: {},
      name: "Start",
      type: "n8n-nodes-base.start"
    },
    {
      parameters: {
        values: {
          string: [
            {
              name: "message",
              value: "{{MESSAGE}}" // 占位符
            }
          ]
        }
      },
      name: "SetMessage",
      type: "n8n-nodes-base.set"
    }
  ]
};

// 替换占位符
const workflowDef = JSON.parse(
  JSON.stringify(template).replace(
    '{{MESSAGE}}', 
    `处理用户 ${$json.userId} 的数据`
  )
);

return { json: { workflow: workflowDef } };
```

## 最佳实践

### 1. 工作流命名

为动态创建的工作流使用描述性名称：

```javascript
const workflowName = `${$json.type}_${$json.userId}_${Date.now()}`;
```

### 2. 错误处理

在工作流定义中包含错误处理节点：

```json
{
  "nodes": [
    {
      "name": "ErrorHandler",
      "type": "n8n-nodes-base.set",
      "parameters": {
        "values": {
          "string": [
            {"name": "error", "value": "处理失败"}
          ]
        }
      }
    }
  ]
}
```

### 3. 资源管理

- 避免创建过于复杂的工作流定义
- 使用合理的并发数量
- 监控临时工作流的创建和清理

### 4. 调试技巧

- 使用"详细结果"输出格式查看执行统计
- 启用"包含执行元数据"获取更多信息
- 在开发阶段使用较小的并发数量

## 故障排除

### 常见错误

1. **工作流定义字段未找到**
   ```
   工作流定义字段 'workflow' 在工作流定义项目 0 中未找到或为空
   ```
   - 检查字段名配置是否正确
   - 确认工作流定义输入包含指定字段

2. **工作流定义格式错误**
   ```
   n8n格式的工作流定义必须包含name和nodes字段
   ```
   - 检查工作流定义是否符合选定的格式
   - 确认必需字段是否存在

3. **临时工作流创建失败**
   ```
   创建临时工作流失败: [错误详情]
   ```
   - 检查n8n实例的API权限
   - 确认工作流定义的有效性

### 调试步骤

1. 检查输入数据格式
2. 验证工作流定义结构
3. 确认节点配置参数
4. 查看执行日志和错误信息

## 示例工作流

查看 `examples/workflow-definition-example.json` 获取完整的示例工作流，展示如何使用工作流定义功能。

## 总结

工作流定义功能为n8n带来了强大的动态执行能力，让您可以：

- 根据数据动态构建工作流
- 实现复杂的条件处理逻辑
- 提高工作流的灵活性和可重用性

通过合理使用这个功能，您可以构建更加智能和自适应的自动化解决方案。
