/**
 * 工作流定义功能测试
 * 验证从连线获取工作流定义的功能
 */

// 模拟工作流定义创建函数
function createTemporaryWorkflow(workflowDefinition, format = 'n8n') {
    // 模拟创建临时工作流的逻辑
    switch (format) {
        case 'n8n':
            if (!workflowDefinition.name || !workflowDefinition.nodes) {
                throw new Error('n8n格式的工作流定义必须包含name和nodes字段');
            }
            break;
        case 'nodes':
            if (!Array.isArray(workflowDefinition)) {
                throw new Error('节点数组格式的工作流定义必须是数组');
            }
            break;
        case 'custom':
            // 自定义格式，假设已经是正确格式
            break;
        default:
            throw new Error(`不支持的工作流定义格式: ${format}`);
    }
    
    // 返回模拟的工作流ID
    return `temp_workflow_${Date.now()}`;
}

// 模拟工作流定义数据
const testWorkflowDefinitions = {
    n8n: {
        name: "测试工作流",
        nodes: [
            {
                parameters: {},
                name: "Start",
                type: "n8n-nodes-base.start",
                position: [240, 300]
            },
            {
                parameters: {
                    values: {
                        string: [{ name: "result", value: "处理完成" }]
                    }
                },
                name: "Set",
                type: "n8n-nodes-base.set",
                position: [460, 300]
            }
        ],
        connections: {
            "Start": { "main": [["Set"]] }
        },
        active: false,
        settings: {},
        staticData: {}
    },
    nodes: [
        {
            parameters: {},
            name: "Start",
            type: "n8n-nodes-base.start"
        },
        {
            parameters: {
                values: {
                    string: [{ name: "result", value: "处理完成" }]
                }
            },
            name: "Set",
            type: "n8n-nodes-base.set"
        }
    ],
    custom: {
        customField: "自定义工作流定义",
        nodes: []
    }
};

console.log("=== 工作流定义功能测试 ===\n");

// 测试1: n8n格式工作流定义
console.log("测试1: n8n格式工作流定义");
try {
    const workflowId = createTemporaryWorkflow(testWorkflowDefinitions.n8n, 'n8n');
    console.log("✅ n8n格式工作流定义测试通过");
    console.log(`生成的工作流ID: ${workflowId}`);
} catch (error) {
    console.log("❌ n8n格式工作流定义测试失败:", error.message);
}

// 测试2: 节点数组格式工作流定义
console.log("\n测试2: 节点数组格式工作流定义");
try {
    const workflowId = createTemporaryWorkflow(testWorkflowDefinitions.nodes, 'nodes');
    console.log("✅ 节点数组格式工作流定义测试通过");
    console.log(`生成的工作流ID: ${workflowId}`);
} catch (error) {
    console.log("❌ 节点数组格式工作流定义测试失败:", error.message);
}

// 测试3: 自定义格式工作流定义
console.log("\n测试3: 自定义格式工作流定义");
try {
    const workflowId = createTemporaryWorkflow(testWorkflowDefinitions.custom, 'custom');
    console.log("✅ 自定义格式工作流定义测试通过");
    console.log(`生成的工作流ID: ${workflowId}`);
} catch (error) {
    console.log("❌ 自定义格式工作流定义测试失败:", error.message);
}

// 测试4: 错误的n8n格式
console.log("\n测试4: 错误的n8n格式");
try {
    const invalidN8n = { name: "测试" }; // 缺少nodes字段
    const workflowId = createTemporaryWorkflow(invalidN8n, 'n8n');
    console.log("❌ 错误的n8n格式测试应该失败但通过了");
} catch (error) {
    console.log("✅ 错误的n8n格式测试正确失败:", error.message);
}

// 测试5: 错误的节点数组格式
console.log("\n测试5: 错误的节点数组格式");
try {
    const invalidNodes = { notArray: true }; // 不是数组
    const workflowId = createTemporaryWorkflow(invalidNodes, 'nodes');
    console.log("❌ 错误的节点数组格式测试应该失败但通过了");
} catch (error) {
    console.log("✅ 错误的节点数组格式测试正确失败:", error.message);
}

// 测试6: 不支持的格式
console.log("\n测试6: 不支持的格式");
try {
    const workflowId = createTemporaryWorkflow({}, 'unsupported');
    console.log("❌ 不支持的格式测试应该失败但通过了");
} catch (error) {
    console.log("✅ 不支持的格式测试正确失败:", error.message);
}

console.log("\n=== 测试完成 ===");

// 模拟工作流定义处理流程
console.log("\n=== 工作流定义处理流程演示 ===");

const inputData = [
    { userId: "user1", action: "process" },
    { userId: "user2", action: "validate" }
];

const workflowDefinitions = [
    { workflow: testWorkflowDefinitions.n8n },
    { workflow: testWorkflowDefinitions.n8n }
];

console.log("输入数据:", JSON.stringify(inputData, null, 2));
console.log("\n工作流定义数量:", workflowDefinitions.length);

// 模拟创建唯一工作流定义
const uniqueDefinitions = new Map();
for (let i = 0; i < inputData.length; i++) {
    const definitionIndex = Math.min(i, workflowDefinitions.length - 1);
    const workflowDef = workflowDefinitions[definitionIndex].workflow;
    const defKey = JSON.stringify(workflowDef);
    if (!uniqueDefinitions.has(defKey)) {
        uniqueDefinitions.set(defKey, workflowDef);
    }
}

console.log(`\n发现 ${uniqueDefinitions.size} 个唯一的工作流定义`);

// 模拟创建临时工作流
const temporaryWorkflowIds = {};
let tempIdCounter = 1;
for (const [defKey, workflowDef] of uniqueDefinitions) {
    const tempWorkflowId = `temp_workflow_${tempIdCounter++}`;
    temporaryWorkflowIds[defKey] = tempWorkflowId;
    console.log(`创建临时工作流: ${tempWorkflowId}`);
}

console.log("\n临时工作流映射:", Object.keys(temporaryWorkflowIds).length, "个");
console.log("演示完成！");
