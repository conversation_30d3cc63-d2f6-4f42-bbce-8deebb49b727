{"name": "并行工作流执行器 - 工作流定义示例", "nodes": [{"parameters": {"values": {"string": [{"name": "userId", "value": "user1"}, {"name": "action", "value": "process"}]}, "options": {}}, "id": "data-input-1", "name": "数据输入1", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "userId", "value": "user2"}, {"name": "action", "value": "validate"}]}, "options": {}}, "id": "data-input-2", "name": "数据输入2", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 480]}, {"parameters": {"values": {"object": [{"name": "workflow", "value": {"name": "动态处理工作流", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "result", "value": "数据处理完成"}, {"name": "timestamp", "value": "{{ new Date().toISOString() }}"}]}}, "name": "处理数据", "type": "n8n-nodes-base.set", "position": [460, 300]}], "connections": {"Start": {"main": [[{"node": "处理数据", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "staticData": {}}}]}, "options": {}}, "id": "workflow-def-1", "name": "工作流定义1", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 600]}, {"parameters": {"values": {"object": [{"name": "workflow", "value": {"name": "动态验证工作流", "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "position": [240, 300]}, {"parameters": {"values": {"string": [{"name": "result", "value": "数据验证完成"}, {"name": "status", "value": "validated"}]}}, "name": "验证数据", "type": "n8n-nodes-base.set", "position": [460, 300]}], "connections": {"Start": {"main": [[{"node": "验证数据", "type": "main", "index": 0}]]}}, "active": false, "settings": {}, "staticData": {}}}]}, "options": {}}, "id": "workflow-def-2", "name": "工作流定义2", "type": "n8n-nodes-base.set", "typeVersion": 3.3, "position": [240, 780]}, {"parameters": {}, "id": "merge-data", "name": "合并数据", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [460, 390]}, {"parameters": {}, "id": "merge-workflows", "name": "合并工作流定义", "type": "n8n-nodes-base.merge", "typeVersion": 2.1, "position": [460, 690]}, {"parameters": {"workflowSource": "workflow", "workflowDefinitionField": "workflow", "workflowDefinitionFormat": "n8n", "dataMode": "each", "concurrency": 2, "onError": "continue", "waitForSubWorkflow": true, "preserveOrder": true, "includeMetadata": true, "outputFormat": "detailed"}, "id": "parallel-executor", "name": "并行工作流执行器", "type": "parallelWorkflowExecutor", "typeVersion": 1, "position": [680, 540]}], "connections": {"数据输入1": {"main": [[{"node": "合并数据", "type": "main", "index": 0}]]}, "数据输入2": {"main": [[{"node": "合并数据", "type": "main", "index": 1}]]}, "工作流定义1": {"main": [[{"node": "合并工作流定义", "type": "main", "index": 0}]]}, "工作流定义2": {"main": [[{"node": "合并工作流定义", "type": "main", "index": 1}]]}, "合并数据": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 0}]]}, "合并工作流定义": {"main": [[{"node": "并行工作流执行器", "type": "main", "index": 1}]]}}, "pinData": {}, "settings": {"executionOrder": "v1"}, "staticData": {}, "tags": [], "triggerCount": 0, "updatedAt": "2024-12-29T00:00:00.000Z", "versionId": "1"}