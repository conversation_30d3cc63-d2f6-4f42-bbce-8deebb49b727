# 并行工作流执行器节点

## 概述

并行工作流执行器节点允许您并行执行多个工作流，支持可控的并发数量。该节点现在支持三种不同的工作流ID获取方式，提供了更大的灵活性。

## 功能特性

- **智能工作流列表**: 自动从n8n实例获取可用工作流列表，支持搜索和选择
- **动态工作流选择**: 支持从输入数据或表达式动态获取工作流ID
- **并发控制**: 可配置的最大并行执行数量
- **错误处理**: 灵活的错误处理策略
- **数据模式**: 支持单独执行每个项目或一次性执行所有项目
- **结果排序**: 可选择保持原始顺序或扁平化结果
- **执行元数据**: 可选择包含执行元数据信息

## 工作流来源模式

### 1. 固定工作流 (fixed)
使用固定的工作流ID，可以从智能工作流列表选择或直接输入ID。

**适用场景**:
- 所有输入项目都需要使用同一个工作流处理
- 工作流ID在设计时已知

**配置**:
- 选择"固定工作流"
- 从智能下拉列表选择工作流（自动从n8n实例获取）或直接输入工作流ID

**智能工作流列表特性**:
- 自动获取当前n8n实例中的所有可用工作流
- 显示工作流名称和ID，便于识别
- 支持搜索功能，快速找到目标工作流
- 如果API调用失败，会显示警告信息并允许手动输入

### 2. 从输入数据获取 (input)
从每个输入项目的指定字段中动态获取工作流ID。

**适用场景**:
- 不同的输入项目需要使用不同的工作流处理
- 工作流ID包含在输入数据中

**配置**:
- 选择"从输入数据获取"
- 指定包含工作流ID的字段名（如：`workflowId`, `workflow_id`, `id`）

**输入数据示例**:
```json
[
  {
    "workflowId": "123",
    "data": "处理数据1"
  },
  {
    "workflowId": "456", 
    "data": "处理数据2"
  }
]
```

### 3. 从表达式获取 (expression)
使用n8n表达式动态计算工作流ID。

**适用场景**:
- 需要基于复杂逻辑计算工作流ID
- 工作流ID需要从其他节点的输出中获取

**配置**:
- 选择"从表达式获取"
- 输入n8n表达式（如：`{{ $json.workflowId }}`, `{{ $item(0).$node["Previous Node"].json.id }}`）

**表达式示例**:
```javascript
// 从当前项目获取
{{ $json.workflowId }}

// 基于条件选择工作流
{{ $json.type === 'A' ? '123' : '456' }}

// 从前一个节点获取
{{ $item(0).$node["Previous Node"].json.workflowId }}
```

### 4. 从连线获取工作流定义 (workflow) 🆕
通过"工作流定义"输入端口接收动态的工作流定义，自动创建临时工作流执行。

**适用场景**:
- 需要动态生成工作流定义
- 工作流结构需要根据输入数据变化
- 需要执行由其他节点构建的工作流

**配置**:
- 选择"从连线获取工作流定义"
- 指定工作流定义字段名（如：`workflow`, `workflowDefinition`, `nodes`）
- 选择工作流定义格式（n8n工作流JSON、节点数组、自定义格式）

**连接方式**:
- 将数据输入连接到"数据输入"端口（第一个输入）
- 将工作流定义连接到"工作流定义"端口（第二个输入）

**工作流定义示例**:
```json
// n8n工作流JSON格式
{
  "workflow": {
    "name": "动态工作流",
    "nodes": [
      {
        "parameters": {},
        "name": "Start",
        "type": "n8n-nodes-base.start",
        "position": [240, 300]
      },
      {
        "parameters": {
          "values": {
            "string": [{"name": "result", "value": "处理完成"}]
          }
        },
        "name": "Set",
        "type": "n8n-nodes-base.set",
        "position": [460, 300]
      }
    ],
    "connections": {
      "Start": {"main": [["Set"]]}
    }
  }
}

// 节点数组格式
{
  "workflow": [
    {
      "parameters": {},
      "name": "Start",
      "type": "n8n-nodes-base.start"
    },
    {
      "parameters": {
        "values": {
          "string": [{"name": "result", "value": "处理完成"}]
        }
      },
      "name": "Set",
      "type": "n8n-nodes-base.set"
    }
  ]
}
```

**特性**:
- 自动创建临时工作流
- 执行完成后自动清理临时工作流
- 支持多种工作流定义格式
- 在"each"模式下支持不同项目使用不同的工作流定义
- 在"once"模式下使用第一个工作流定义

## 数据模式

### 每个输入项目单独执行 (each)
为每个输入项目单独执行一次工作流。这是默认模式。

**特点**:
- 每个输入项目都会触发一次工作流执行
- 支持不同项目使用不同的工作流ID
- 结果可以保持原始顺序

### 所有输入项目一次执行 (once)
将所有输入项目一次性传递给工作流执行。

**特点**:
- 所有输入项目作为一个批次传递给工作流
- 在动态模式下，所有项目必须使用相同的工作流ID
- 适合批量处理场景

## 输出格式选项

并行工作流执行器支持多种输出格式，让您可以根据需要自定义结果的组织方式：

### 1. 默认格式 (default)
使用传统的输出格式，基于"保持顺序"设置来决定结果结构。

**特点**:
- 如果启用"保持顺序"，返回二维数组，每个子数组对应一个输入项目的结果
- 如果禁用"保持顺序"，返回扁平化的一维数组

**输出示例**:
```json
// 保持顺序时
[
  [{"result": "data1"}],
  [{"result": "data2"}]
]

// 不保持顺序时
[
  {"result": "data1"},
  {"result": "data2"}
]
```

### 2. 扁平化结果 (flat)
将所有结果合并到一个数组中，忽略"保持顺序"设置。

**适用场景**:
- 需要简单的结果列表
- 不关心结果的原始分组

**输出示例**:
```json
[
  {"result": "data1"},
  {"result": "data2"},
  {"result": "data3"}
]
```

### 3. 分组结果 (grouped)
按指定字段对结果进行分组，便于分析和处理。

**配置**:
- 设置"分组字段"参数（默认为 `workflowId`）
- 可以按工作流ID、状态或任何自定义字段分组

**输出示例**:
```json
[
  {
    "workflowId": "123",
    "items": [
      {"result": "data1"},
      {"result": "data2"}
    ],
    "count": 2
  },
  {
    "workflowId": "456",
    "items": [
      {"result": "data3"}
    ],
    "count": 1
  }
]
```

### 4. 详细结果 (detailed)
包含完整的执行统计信息和元数据。

**适用场景**:
- 需要监控执行性能
- 需要详细的执行报告

**输出示例**:
```json
[
  {
    "results": [
      {"result": "data1"},
      {"result": "data2"}
    ],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1,
      "executionTime": 1500
    },
    "metadata": {
      "outputFormat": "detailed",
      "preserveOrder": true,
      "includeMetadata": false,
      "workflowIds": ["123", "456"]
    }
  }
]
```

### 5. 自定义结构 (custom)
使用JSON模板自定义输出结构。

**配置**:
- 在"自定义结构模板"中定义JSON模板
- 支持以下变量：
  - `{{results}}`: 所有结果数据
  - `{{total}}`: 总执行数量
  - `{{successful}}`: 成功执行数量
  - `{{failed}}`: 失败执行数量
  - `{{executionTime}}`: 总执行时间（毫秒）
  - `{{workflowId}}`: 工作流ID列表

**模板示例**:
```json
{
  "data": {{results}},
  "stats": {
    "processed": {{total}},
    "success_rate": "{{successful}}/{{total}}",
    "duration_ms": {{executionTime}}
  },
  "workflows": {{workflowId}}
}
```

**输出示例**:
```json
[
  {
    "data": [
      {"result": "data1"},
      {"result": "data2"}
    ],
    "stats": {
      "processed": 2,
      "success_rate": "2/2",
      "duration_ms": 1200
    },
    "workflows": ["123", "456"]
  }
]
```

## 配置参数

| 参数 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| 工作流来源 | 选项 | fixed | 工作流ID的获取方式 |
| 工作流 | 资源定位器 | - | 固定工作流的选择（仅在固定模式下显示） |
| 工作流ID字段 | 字符串 | workflowId | 输入数据中包含工作流ID的字段名 |
| 工作流ID表达式 | 字符串 | - | 用于计算工作流ID的表达式 |
| 工作流定义字段 | 字符串 | workflow | 工作流定义输入中包含工作流定义的字段名 |
| 工作流定义格式 | 选项 | n8n | 工作流定义的数据格式 |
| 数据模式 | 选项 | each | 如何将输入数据传递给工作流 |
| 并发数 | 数字 | 5 | 最大并行执行数量（1-50） |
| 错误处理 | 选项 | continue | 工作流执行失败时的处理方式 |
| 等待子工作流 | 布尔 | true | 是否等待子工作流完成执行 |
| 保持顺序 | 布尔 | true | 是否保持结果的原始顺序 |
| 包含执行元数据 | 布尔 | false | 是否在结果中包含执行元数据 |
| 输出格式 | 选项 | default | 结果的输出格式和组织方式 |
| 自定义结构模板 | JSON | - | 自定义输出结构的JSON模板（仅在自定义格式下显示） |
| 分组字段 | 字符串 | workflowId | 用于分组结果的字段名（仅在分组格式下显示） |

## 使用示例

### 示例1: 基于输入数据的动态工作流执行

```json
// 输入数据
[
  {"workflowId": "123", "userId": "user1", "action": "process"},
  {"workflowId": "456", "userId": "user2", "action": "validate"}
]

// 配置
{
  "workflowSource": "input",
  "workflowIdField": "workflowId",
  "dataMode": "each",
  "concurrency": 3
}
```

### 示例2: 基于表达式的条件工作流选择

```json
// 输入数据
[
  {"type": "premium", "data": "重要数据"},
  {"type": "standard", "data": "普通数据"}
]

// 配置
{
  "workflowSource": "expression", 
  "workflowIdExpression": "{{ $json.type === 'premium' ? '123' : '456' }}",
  "dataMode": "each"
}
```

### 示例3: 使用分组输出格式

```json
// 输入数据
[
  {"workflowId": "123", "category": "A", "data": "数据1"},
  {"workflowId": "123", "category": "A", "data": "数据2"},
  {"workflowId": "456", "category": "B", "data": "数据3"}
]

// 配置
{
  "workflowSource": "input",
  "workflowIdField": "workflowId",
  "outputFormat": "grouped",
  "groupByField": "workflowId"
}

// 输出结果
[
  {
    "workflowId": "123",
    "items": [
      {"processed": "数据1", "status": "success"},
      {"processed": "数据2", "status": "success"}
    ],
    "count": 2
  },
  {
    "workflowId": "456",
    "items": [
      {"processed": "数据3", "status": "success"}
    ],
    "count": 1
  }
]
```

### 示例4: 使用详细输出格式监控执行

```json
// 配置
{
  "workflowSource": "fixed",
  "workflowId": {"mode": "id", "value": "123"},
  "outputFormat": "detailed",
  "includeMetadata": true
}

// 输出结果
[
  {
    "results": [
      {"result": "处理结果1"},
      {"result": "处理结果2"}
    ],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1,
      "executionTime": 2500
    },
    "metadata": {
      "outputFormat": "detailed",
      "preserveOrder": true,
      "includeMetadata": true,
      "workflowIds": ["123"]
    }
  }
]
```

### 示例5: 使用自定义模板格式

```json
// 自定义模板
{
  "report": {
    "data": {{results}},
    "execution_summary": {
      "total_items": {{total}},
      "success_count": {{successful}},
      "failure_count": {{failed}},
      "processing_time_ms": {{executionTime}},
      "success_rate": "{{successful}}/{{total}}"
    },
    "workflow_info": {{workflowId}},
    "timestamp": "2024-12-29T10:30:00Z"
  }
}

// 输出结果
[
  {
    "report": {
      "data": [
        {"result": "数据1"},
        {"result": "数据2"}
      ],
      "execution_summary": {
        "total_items": 2,
        "success_count": 2,
        "failure_count": 0,
        "processing_time_ms": 1800,
        "success_rate": "2/2"
      },
      "workflow_info": ["123"],
      "timestamp": "2024-12-29T10:30:00Z"
    }
  }
]
```

## 注意事项

1. **工作流ID验证**: 确保提供的工作流ID存在且可访问
2. **并发限制**: 合理设置并发数，避免系统资源过载
3. **错误处理**: 根据业务需求选择合适的错误处理策略
4. **数据模式选择**: 在"一次执行"模式下使用动态工作流时，所有项目必须使用相同的工作流ID
5. **输出格式选择**:
   - 默认格式适合大多数场景
   - 分组格式适合需要按类别分析结果的场景
   - 详细格式适合监控和调试
   - 自定义格式提供最大灵活性，但需要正确的JSON模板语法
6. **自定义模板语法**:
   - 使用双花括号 `{{variable}}` 包围变量名
   - 确保模板是有效的JSON格式
   - 变量替换后的结果必须是有效的JSON值
7. **性能考虑**: 复杂的输出格式（如详细格式和自定义格式）可能会增加少量处理时间

## 技术说明

### 工作流列表获取机制

节点使用n8n的内部REST API来获取工作流列表：

1. **API端点**: 通过`this.getRestApiUrl()`获取n8n实例的REST API基础URL
2. **请求方式**: 使用`this.helpers.httpRequest()`发送GET请求到`/workflows`端点
3. **数据转换**: 将API响应转换为`INodeListSearchResult`格式，包含工作流名称和ID
4. **错误处理**: 如果API调用失败，显示警告信息并提供手动输入选项
5. **用户体验**: 支持搜索功能，便于在大量工作流中快速定位

### 优化的并发控制算法

节点使用了高效的滑动窗口并发控制算法：

#### 核心特性
- **滑动窗口**: 动态管理执行池，任务完成后立即启动新任务
- **内存优化**: 避免创建大量Promise对象，减少内存占用
- **错误隔离**: 单个任务失败不影响其他任务执行
- **灵活配置**: 支持"停止所有"和"继续执行"两种错误处理策略

#### 算法优势
1. **高效率**: 相比批处理，滑动窗口能更好地利用系统资源
2. **低延迟**: 任务完成后立即启动新任务，减少等待时间
3. **简洁性**: 使用专门的ConcurrencyController类，代码更清晰
4. **可扩展**: 支持多种并发控制策略（滑动窗口、批处理、简单限制）

### API调用示例

```typescript
const response = await this.helpers.httpRequest({
  method: 'GET',
  url: `${baseUrl}/workflows`,
  json: true,
  headers: {
    'accept': 'application/json',
  },
});
```

## 更新日志

### v1.5.0 (2024-12-29)
- 🔗 **新增动态工作流定义支持**
  - 支持通过连线传递工作流定义
  - 添加"工作流定义"输入端口
  - 自动创建和管理临时工作流
  - 执行完成后自动清理临时工作流
- 🏗️ **多种工作流定义格式**
  - 支持n8n标准工作流JSON格式
  - 支持节点数组格式
  - 支持自定义格式
  - 灵活的格式转换和处理
- 🎯 **增强的动态执行能力**
  - 在"each"模式下支持不同项目使用不同工作流定义
  - 在"once"模式下使用第一个工作流定义
  - 优化的临时工作流管理和资源清理
- 📚 **完善的文档和示例**
  - 新增工作流定义使用指南
  - 提供完整的示例工作流
  - 详细的配置参数说明

### v1.4.0 (2024-12-29)
- 🎨 **新增灵活的输出格式选项**
  - 支持5种输出格式：默认、扁平化、分组、详细、自定义
  - 添加自定义JSON模板功能，支持变量替换
  - 新增分组功能，可按任意字段分组结果
  - 详细格式包含完整的执行统计和元数据
- 📊 **增强执行监控**
  - 自动计算执行时间和成功率
  - 提供详细的执行统计信息
  - 支持自定义报告格式
- 🔧 **改进用户体验**
  - 更灵活的结果组织方式
  - 更好的数据分析支持
  - 增强的错误信息格式化

### v1.3.0 (2024-12-29)
- ⚡ 优化并发控制算法，使用滑动窗口机制
- 🏗️ 重构代码结构，引入专门的ConcurrencyController类
- 📈 提升执行效率和资源利用率
- 🧹 简化代码逻辑，提高可维护性
- 💾 优化内存使用，减少Promise对象创建

### v1.2.0
- 实现真正的工作流列表获取功能
- 连接到n8n内部API获取实际工作流数据
- 改进工作流选择用户体验
- 添加搜索和过滤功能
- 增强错误处理和回退机制

### v1.1.0
- 新增动态工作流ID支持
- 支持从输入数据字段获取工作流ID
- 支持从表达式计算工作流ID
- 改进错误处理和验证
- 更新节点subtitle显示动态信息
