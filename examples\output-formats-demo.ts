#!/usr/bin/env node
/**
 * 并行工作流执行器输出格式演示
 * 展示不同输出格式的使用方法和效果
 */

// 模拟输入数据
const sampleInputData = [
  { workflowId: "123", category: "A", data: "数据1" },
  { workflowId: "123", category: "A", data: "数据2" },
  { workflowId: "456", category: "B", data: "数据3" },
  { workflowId: "456", category: "B", data: "数据4" },
];

// 模拟执行结果
const sampleResults = [
  [{ json: { processed: "数据1", status: "success" } }],
  [{ json: { processed: "数据2", status: "success" } }],
  [{ json: { processed: "数据3", status: "success" } }],
  [{ json: { processed: "数据4", status: "failed", error: "处理失败" } }],
];

const sampleWorkflowIds = ["123", "123", "456", "456"];
const sampleStats = {
  total: 4,
  successful: 3,
  failed: 1,
  executionTime: 2500,
};

console.log("=== 并行工作流执行器输出格式演示 ===\n");

// 演示1: 默认格式（保持顺序）
console.log("1. 默认格式（保持顺序）:");
console.log("配置: { outputFormat: 'default', preserveOrder: true }");
console.log("结果: 二维数组，每个子数组对应一个输入项目");
console.log(JSON.stringify(sampleResults, null, 2));
console.log("\n" + "=".repeat(50) + "\n");

// 演示2: 扁平化格式
console.log("2. 扁平化格式:");
console.log("配置: { outputFormat: 'flat' }");
console.log("结果: 所有结果合并到一个数组");
const flatResults = sampleResults.flat().map(item => item.json);
console.log(JSON.stringify([flatResults], null, 2));
console.log("\n" + "=".repeat(50) + "\n");

// 演示3: 分组格式
console.log("3. 分组格式:");
console.log("配置: { outputFormat: 'grouped', groupByField: 'workflowId' }");
console.log("结果: 按工作流ID分组");
const groupedResults = [
  {
    workflowId: "123",
    items: [
      { processed: "数据1", status: "success" },
      { processed: "数据2", status: "success" }
    ],
    count: 2
  },
  {
    workflowId: "456",
    items: [
      { processed: "数据3", status: "success" },
      { processed: "数据4", status: "failed", error: "处理失败" }
    ],
    count: 2
  }
];
console.log(JSON.stringify([groupedResults], null, 2));
console.log("\n" + "=".repeat(50) + "\n");

// 演示4: 详细格式
console.log("4. 详细格式:");
console.log("配置: { outputFormat: 'detailed' }");
console.log("结果: 包含完整统计信息和元数据");
const detailedResults = [{
  results: flatResults,
  summary: sampleStats,
  metadata: {
    outputFormat: "detailed",
    preserveOrder: true,
    includeMetadata: false,
    workflowIds: ["123", "456"]
  }
}];
console.log(JSON.stringify([detailedResults], null, 2));
console.log("\n" + "=".repeat(50) + "\n");

// 演示5: 自定义格式
console.log("5. 自定义格式:");
console.log("配置: { outputFormat: 'custom' }");
console.log("模板:");
const customTemplate = {
  report: {
    data: "{{results}}",
    execution_summary: {
      total_items: "{{total}}",
      success_count: "{{successful}}",
      failure_count: "{{failed}}",
      processing_time_ms: "{{executionTime}}",
      success_rate: "{{successful}}/{{total}}"
    },
    workflow_info: "{{workflowId}}",
    timestamp: "2024-12-29T10:30:00Z"
  }
};
console.log(JSON.stringify(customTemplate, null, 2));

console.log("\n结果:");
const customResults = [{
  report: {
    data: flatResults,
    execution_summary: {
      total_items: 4,
      success_count: 3,
      failure_count: 1,
      processing_time_ms: 2500,
      success_rate: "3/4"
    },
    workflow_info: ["123", "456"],
    timestamp: "2024-12-29T10:30:00Z"
  }
}];
console.log(JSON.stringify([customResults], null, 2));
console.log("\n" + "=".repeat(50) + "\n");

console.log("演示完成！");
console.log("\n使用建议:");
console.log("- 默认格式: 适合大多数场景，保持与现有工作流的兼容性");
console.log("- 扁平化格式: 适合需要简单列表的场景");
console.log("- 分组格式: 适合需要按类别分析结果的场景");
console.log("- 详细格式: 适合监控、调试和性能分析");
console.log("- 自定义格式: 适合需要特定报告格式的场景");
